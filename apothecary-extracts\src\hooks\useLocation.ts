import { useEffect } from 'react';
import { useLocationStore } from '@/store/locationStore';
import { useCartStore } from '@/store/cartStore';
import { useUIStore } from '@/store/uiStore';
import { toast } from '@/store/uiStore';
import type { Location } from '@/lib/types';

/**
 * Custom hook for location management
 */
export function useLocation() {
  const {
    selectedLocation,
    availableLocations,
    isLoading,
    error,
    setSelectedLocation,
    setAvailableLocations,
    clearSelectedLocation,
    setLoading,
    setError,
    getLocationById,
  } = useLocationStore();

  const { cart, switchLocation, clearCart } = useCartStore();
  const { setLocationSelectorOpen } = useUIStore();

  // Load available locations on mount
  useEffect(() => {
    loadLocations();
  }, []);

  // Mock function to load locations (replace with actual API call)
  const loadLocations = async () => {
    setLoading(true);
    setError(null);

    try {
      // Mock locations data
      const mockLocations: Location[] = [
        {
          id: 'loc_1',
          name: 'Apothecary Extracts - Downtown',
          address: {
            id: 'addr_1',
            type: 'work',
            street: '123 Main Street',
            city: 'Boston',
            state: 'MA',
            zipCode: '02101',
            isDefault: true,
          },
          phone: '(*************',
          email: '<EMAIL>',
          hours: {
            monday: { open: '10:00', close: '20:00', isClosed: false },
            tuesday: { open: '10:00', close: '20:00', isClosed: false },
            wednesday: { open: '10:00', close: '20:00', isClosed: false },
            thursday: { open: '10:00', close: '20:00', isClosed: false },
            friday: { open: '10:00', close: '20:00', isClosed: false },
            saturday: { open: '10:00', close: '18:00', isClosed: false },
            sunday: { open: '12:00', close: '18:00', isClosed: false },
          },
          services: ['recreational', 'medical', 'delivery', 'curbside'],
          image: '/images/locations/downtown.jpg',
          description: 'Our flagship location in the heart of downtown Boston, featuring our full selection of premium cannabis products and expert consultation services.',
          amenities: ['Parking Available', 'Wheelchair Accessible', 'Consultation Room', 'Express Pickup'],
          coordinates: { lat: 42.3601, lng: -71.0589 },
          isActive: true,
        },
        {
          id: 'loc_2',
          name: 'Apothecary Extracts - Cambridge',
          address: {
            id: 'addr_2',
            type: 'work',
            street: '456 Harvard Avenue',
            city: 'Cambridge',
            state: 'MA',
            zipCode: '02138',
            isDefault: false,
          },
          phone: '(*************',
          email: '<EMAIL>',
          hours: {
            monday: { open: '10:00', close: '20:00', isClosed: false },
            tuesday: { open: '10:00', close: '20:00', isClosed: false },
            wednesday: { open: '10:00', close: '20:00', isClosed: false },
            thursday: { open: '10:00', close: '20:00', isClosed: false },
            friday: { open: '10:00', close: '20:00', isClosed: false },
            saturday: { open: '10:00', close: '18:00', isClosed: false },
            sunday: { open: '12:00', close: '18:00', isClosed: false },
          },
          services: ['recreational', 'medical', 'curbside'],
          image: '/images/locations/cambridge.jpg',
          description: 'Located near Harvard University, our Cambridge location offers a curated selection of products perfect for the academic community.',
          amenities: ['Street Parking', 'Wheelchair Accessible', 'Student Discounts', 'Educational Resources'],
          coordinates: { lat: 42.3736, lng: -71.1097 },
          isActive: true,
        },
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setAvailableLocations(mockLocations);
      
      // Auto-select first location if none selected
      if (!selectedLocation && mockLocations.length > 0) {
        setSelectedLocation(mockLocations[0]);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load locations';
      setError(errorMessage);
      toast.error('Error', 'Failed to load store locations');
    } finally {
      setLoading(false);
    }
  };

  // Handle location selection with cart warning
  const handleLocationChange = (newLocation: Location) => {
    if (cart && cart.items.length > 0 && cart.locationId !== newLocation.id) {
      // Show warning about cart clearing
      const confirmed = window.confirm(
        'Changing locations will clear your current cart. Do you want to continue?'
      );
      
      if (!confirmed) {
        return false;
      }
    }

    setSelectedLocation(newLocation);
    switchLocation(newLocation.id);
    setLocationSelectorOpen(false);
    
    toast.success('Location Changed', `Now shopping at ${newLocation.name}`);
    return true;
  };

  // Get current location status
  const getCurrentLocationStatus = () => {
    if (!selectedLocation) return 'closed';
    
    const now = new Date();
    const currentDay = now.toLocaleLowerCase() as keyof typeof selectedLocation.hours;
    const todayHours = selectedLocation.hours[currentDay];
    
    if (todayHours.isClosed) return 'closed';
    
    const currentTime = now.getHours() * 100 + now.getMinutes();
    const openTime = parseInt(todayHours.open.replace(':', ''));
    const closeTime = parseInt(todayHours.close.replace(':', ''));
    
    if (currentTime >= openTime && currentTime < closeTime) {
      return 'open';
    } else if (currentTime < openTime) {
      return 'opening-soon';
    } else {
      return 'closed';
    }
  };

  // Get next opening time
  const getNextOpeningTime = () => {
    if (!selectedLocation) return null;
    
    const now = new Date();
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    
    for (let i = 0; i < 7; i++) {
      const checkDate = new Date(now);
      checkDate.setDate(now.getDate() + i);
      
      const dayName = days[checkDate.getDay()] as keyof typeof selectedLocation.hours;
      const dayHours = selectedLocation.hours[dayName];
      
      if (!dayHours.isClosed) {
        if (i === 0) {
          // Today - check if still opening
          const currentTime = now.getHours() * 100 + now.getMinutes();
          const openTime = parseInt(dayHours.open.replace(':', ''));
          
          if (currentTime < openTime) {
            return {
              day: 'Today',
              time: dayHours.open,
            };
          }
        } else {
          // Future day
          return {
            day: checkDate.toLocaleDateString('en-US', { weekday: 'long' }),
            time: dayHours.open,
          };
        }
      }
    }
    
    return null;
  };

  // Check if location offers specific service
  const hasService = (service: string) => {
    return selectedLocation?.services.includes(service as any) || false;
  };

  // Get distance to location (mock implementation)
  const getDistanceToLocation = (locationId: string) => {
    // Mock distance calculation
    const distances: Record<string, number> = {
      'loc_1': 2.3,
      'loc_2': 4.7,
    };
    
    return distances[locationId] || 0;
  };

  return {
    selectedLocation,
    availableLocations,
    isLoading,
    error,
    locationStatus: getCurrentLocationStatus(),
    nextOpening: getNextOpeningTime(),
    handleLocationChange,
    clearSelectedLocation,
    getLocationById,
    hasService,
    getDistanceToLocation,
    loadLocations,
  };
}
