import { useEffect } from 'react';
import { useCartStore } from '@/store/cartStore';
import { useLocationStore } from '@/store/locationStore';
import { toast } from '@/store/uiStore';
import { formatCurrency } from '@/lib/utils';
import type { Product, ProductVariant } from '@/lib/types';

/**
 * Custom hook for cart management
 */
export function useCart() {
  const {
    cart,
    isLoading,
    error,
    initializeCart,
    addItem,
    removeItem,
    updateItemQuantity,
    clearCart,
    switchLocation,
    calculateTotals,
    getItemCount,
    getItemById,
    hasItem,
  } = useCartStore();

  const { selectedLocation } = useLocationStore();

  // Initialize cart when location is selected
  useEffect(() => {
    if (selectedLocation && !cart) {
      initializeCart(selectedLocation.id);
    }
  }, [selectedLocation, cart, initializeCart]);

  // Handle adding item to cart
  const handleAddToCart = (
    product: Product,
    variant: ProductVariant,
    quantity: number = 1
  ) => {
    if (!selectedLocation) {
      toast.error('Location Required', 'Please select a location before adding items to cart');
      return false;
    }

    if (!variant.isActive || !variant.inventory.isInStock) {
      toast.error('Out of Stock', 'This item is currently out of stock');
      return false;
    }

    if (quantity > variant.inventory.available) {
      toast.error(
        'Insufficient Stock',
        `Only ${variant.inventory.available} items available`
      );
      return false;
    }

    try {
      addItem({
        productId: product.id,
        variantId: variant.id,
        quantity,
        price: variant.salePrice || variant.price,
      });

      toast.success(
        'Added to Cart',
        `${product.name} (${variant.name}) added to cart`
      );

      return true;
    } catch (err) {
      toast.error('Error', 'Failed to add item to cart');
      return false;
    }
  };

  // Handle removing item from cart
  const handleRemoveFromCart = (itemId: string) => {
    try {
      removeItem(itemId);
      toast.success('Removed', 'Item removed from cart');
    } catch (err) {
      toast.error('Error', 'Failed to remove item from cart');
    }
  };

  // Handle updating item quantity
  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 0) return;

    try {
      updateItemQuantity(itemId, newQuantity);
      
      if (newQuantity === 0) {
        toast.success('Removed', 'Item removed from cart');
      }
    } catch (err) {
      toast.error('Error', 'Failed to update item quantity');
    }
  };

  // Handle clearing entire cart
  const handleClearCart = () => {
    const confirmed = window.confirm('Are you sure you want to clear your cart?');
    
    if (confirmed) {
      clearCart();
      toast.success('Cart Cleared', 'All items removed from cart');
    }
  };

  // Get cart summary
  const getCartSummary = () => {
    if (!cart) {
      return {
        itemCount: 0,
        subtotal: 0,
        tax: 0,
        total: 0,
        isEmpty: true,
      };
    }

    return {
      itemCount: getItemCount(),
      subtotal: cart.subtotal,
      tax: cart.tax,
      total: cart.total,
      isEmpty: cart.items.length === 0,
    };
  };

  // Get formatted cart totals
  const getFormattedTotals = () => {
    const summary = getCartSummary();
    
    return {
      subtotal: formatCurrency(summary.subtotal),
      tax: formatCurrency(summary.tax),
      total: formatCurrency(summary.total),
    };
  };

  // Check if product variant is in cart
  const isInCart = (productId: string, variantId: string) => {
    return hasItem(productId, variantId);
  };

  // Get quantity of specific item in cart
  const getItemQuantity = (productId: string, variantId: string) => {
    if (!cart) return 0;
    
    const item = cart.items.find(
      item => item.productId === productId && item.variantId === variantId
    );
    
    return item?.quantity || 0;
  };

  // Validate cart before checkout
  const validateCart = () => {
    if (!cart || cart.items.length === 0) {
      toast.error('Empty Cart', 'Your cart is empty');
      return false;
    }

    if (!selectedLocation) {
      toast.error('Location Required', 'Please select a location');
      return false;
    }

    // Check inventory for each item
    for (const item of cart.items) {
      // In a real app, you'd fetch current inventory here
      // For now, we'll assume items are still available
    }

    return true;
  };

  // Get cart items with product details (mock implementation)
  const getCartItemsWithDetails = () => {
    if (!cart) return [];

    // In a real app, you'd fetch product details for each cart item
    // For now, return cart items as-is
    return cart.items.map(item => ({
      ...item,
      // Mock product details
      name: `Product ${item.productId}`,
      image: '/images/products/placeholder.jpg',
      brand: 'Apothecary Extracts',
    }));
  };

  // Calculate savings from sale prices
  const getTotalSavings = () => {
    if (!cart) return 0;

    // In a real app, you'd calculate savings based on original vs sale prices
    // For now, return 0
    return 0;
  };

  return {
    cart,
    isLoading,
    error,
    cartSummary: getCartSummary(),
    formattedTotals: getFormattedTotals(),
    cartItemsWithDetails: getCartItemsWithDetails(),
    totalSavings: getTotalSavings(),
    
    // Actions
    handleAddToCart,
    handleRemoveFromCart,
    handleUpdateQuantity,
    handleClearCart,
    
    // Utilities
    isInCart,
    getItemQuantity,
    validateCart,
    getItemById,
  };
}
