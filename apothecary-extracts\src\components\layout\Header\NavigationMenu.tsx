import React, { useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui/Icon';
import { mainNavigation } from '@/data/navigation';

export const NavigationMenu: React.FC = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const handleMouseEnter = (label: string) => {
    setActiveDropdown(label);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  return (
    <nav className="flex items-center space-x-8" role="navigation">
      {mainNavigation.map((item) => (
        <div
          key={item.label}
          className="relative"
          onMouseEnter={() => item.children && handleMouseEnter(item.label)}
          onMouseLeave={handleMouseLeave}
        >
          {/* Main Navigation Item */}
          <Link
            href={item.href}
            className={cn(
              'flex items-center space-x-1 px-3 py-2 text-sm font-medium text-neutral-charcoal hover:text-primary-sage transition-colors duration-200',
              activeDropdown === item.label && 'text-primary-sage'
            )}
          >
            <span>{item.label}</span>
            {item.children && (
              <Icon 
                name="chevron-down" 
                size="sm" 
                className={cn(
                  'transition-transform duration-200',
                  activeDropdown === item.label && 'rotate-180'
                )}
              />
            )}
          </Link>

          {/* Dropdown Menu */}
          {item.children && (
            <div
              className={cn(
                'absolute top-full left-0 mt-1 w-64 bg-neutral-white border border-neutral-stone rounded-lg shadow-lg transition-all duration-200 z-50',
                activeDropdown === item.label 
                  ? 'opacity-100 visible translate-y-0' 
                  : 'opacity-0 invisible -translate-y-2'
              )}
            >
              <div className="py-2">
                {item.children.map((child) => (
                  <Link
                    key={child.label}
                    href={child.href}
                    className="block px-4 py-3 text-sm text-neutral-charcoal hover:bg-neutral-cream hover:text-primary-sage transition-colors duration-200"
                  >
                    <div className="font-medium">{child.label}</div>
                    {child.description && (
                      <div className="text-xs text-neutral-charcoal/60 mt-1">
                        {child.description}
                      </div>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </nav>
  );
};

export default NavigationMenu;
