import React from 'react';
import { cn } from '@/lib/utils';
import { Section } from '@/components/layout/Section';
import { Button } from '@/components/ui/Button';
import { Icon, type IconName } from '@/components/ui/Icon';

export interface HeroProps {
  title: string;
  subtitle?: string;
  description?: string;
  backgroundImage?: string;
  primaryCTA?: {
    text: string;
    href?: string;
    onClick?: () => void;
    icon?: IconName;
  };
  secondaryCTA?: {
    text: string;
    href?: string;
    onClick?: () => void;
    icon?: IconName;
  };
  overlay?: boolean;
  overlayOpacity?: 'light' | 'medium' | 'dark';
  textAlign?: 'left' | 'center' | 'right';
  className?: string;
}

export const Hero: React.FC<HeroProps> = ({
  title,
  subtitle,
  description,
  backgroundImage,
  primaryCTA,
  secondaryCTA,
  overlay = true,
  overlayOpacity = 'medium',
  textAlign = 'center',
  className,
}) => {
  const handleCTAClick = (cta: HeroProps['primaryCTA']) => {
    if (cta?.onClick) {
      cta.onClick();
    } else if (cta?.href) {
      window.location.href = cta.href;
    }
  };

  return (
    <Section
      background={backgroundImage ? 'image' : 'sage'}
      backgroundImage={backgroundImage}
      overlay={overlay}
      overlayOpacity={overlayOpacity}
      padding="xl"
      className={cn('min-h-[60vh] flex items-center', className)}
    >
      <div className={cn(
        'w-full',
        textAlign === 'center' && 'text-center',
        textAlign === 'right' && 'text-right'
      )}>
        {/* Subtitle */}
        {subtitle && (
          <p className="text-secondary-amber font-medium text-lg mb-4 tracking-wide uppercase">
            {subtitle}
          </p>
        )}

        {/* Title */}
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-white mb-6 font-accent leading-tight">
          {title}
        </h1>

        {/* Description */}
        {description && (
          <p className="text-xl md:text-2xl text-neutral-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            {description}
          </p>
        )}

        {/* CTAs */}
        {(primaryCTA || secondaryCTA) && (
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {primaryCTA && (
              <Button
                variant="accent"
                size="lg"
                onClick={() => handleCTAClick(primaryCTA)}
                leftIcon={primaryCTA.icon}
                className="min-w-[200px]"
              >
                {primaryCTA.text}
              </Button>
            )}

            {secondaryCTA && (
              <Button
                variant="ghost"
                size="lg"
                onClick={() => handleCTAClick(secondaryCTA)}
                leftIcon={secondaryCTA.icon}
                className="min-w-[200px] text-neutral-white border-neutral-white hover:bg-neutral-white hover:text-primary-sage"
              >
                {secondaryCTA.text}
              </Button>
            )}
          </div>
        )}
      </div>
    </Section>
  );
};

export default Hero;
