import { useEffect } from 'react';
import { useAgeVerificationStore } from '@/store/ageVerificationStore';
import { useUIStore } from '@/store/uiStore';

/**
 * Custom hook for age verification logic
 */
export function useAgeVerification() {
  const {
    isVerified,
    dateOfBirth,
    verificationDate,
    verifyAge,
    setVerified,
    reset,
    isLegalAge,
  } = useAgeVerificationStore();

  const { setAgeGateOpen } = useUIStore();

  // Check if age verification is needed on mount
  useEffect(() => {
    if (!isVerified) {
      setAgeGateOpen(true);
    }
  }, [isVerified, setAgeGateOpen]);

  // Check if verification has expired (optional - could be 24 hours, 30 days, etc.)
  const isVerificationExpired = () => {
    if (!verificationDate) return true;
    
    const verificationTime = new Date(verificationDate).getTime();
    const now = new Date().getTime();
    const hoursSinceVerification = (now - verificationTime) / (1000 * 60 * 60);
    
    // Expire after 24 hours
    return hoursSinceVerification > 24;
  };

  // Handle age verification
  const handleVerifyAge = (birthDate: string) => {
    const isValid = verifyAge(birthDate);
    
    if (isValid) {
      setAgeGateOpen(false);
    }
    
    return isValid;
  };

  // Handle age verification denial
  const handleDenyAge = () => {
    setVerified(false);
    // Redirect to external site or show denial message
    if (typeof window !== 'undefined') {
      window.location.href = 'https://www.samhsa.gov/';
    }
  };

  // Force re-verification
  const forceReVerification = () => {
    reset();
    setAgeGateOpen(true);
  };

  // Get user age
  const getUserAge = () => {
    if (!dateOfBirth) return null;
    
    const birth = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  return {
    isVerified,
    dateOfBirth,
    verificationDate,
    isVerificationExpired: isVerificationExpired(),
    userAge: getUserAge(),
    isLegalAge: isLegalAge(),
    handleVerifyAge,
    handleDenyAge,
    forceReVerification,
    reset,
  };
}
