import React from 'react';
import { cn } from '@/lib/utils';
import { Icon, type IconName } from '../Icon';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  loading?: boolean;
  leftIcon?: IconName;
  rightIcon?: IconName;
  children: React.ReactNode;
}

const buttonVariants = {
  primary: 'bg-primary-sage text-neutral-white hover:bg-primary-forest focus:ring-primary-sage/20 shadow-sm',
  secondary: 'bg-neutral-white text-primary-sage border border-primary-sage hover:bg-neutral-cream focus:ring-primary-sage/20',
  accent: 'bg-secondary-amber text-neutral-charcoal hover:bg-secondary-clay focus:ring-secondary-amber/20 shadow-sm',
  ghost: 'bg-transparent text-primary-sage hover:bg-neutral-cream focus:ring-primary-sage/20',
  link: 'bg-transparent text-primary-sage hover:text-primary-forest underline-offset-4 hover:underline focus:ring-primary-sage/20',
} as const;

const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
} as const;

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  loading = false,
  leftIcon,
  rightIcon,
  disabled,
  className,
  children,
  ...props
}) => {
  const isDisabled = disabled || loading;

  return (
    <button
      className={cn(
        // Base styles
        'inline-flex items-center justify-center gap-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed',
        
        // Variant styles
        buttonVariants[variant],
        
        // Size styles
        buttonSizes[size],
        
        // Full width
        fullWidth && 'w-full',
        
        // Loading state
        loading && 'cursor-wait',
        
        // Custom className
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {loading && (
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
      )}
      
      {leftIcon && !loading && (
        <Icon name={leftIcon} size="sm" />
      )}
      
      <span className={cn(loading && 'opacity-70')}>
        {children}
      </span>
      
      {rightIcon && !loading && (
        <Icon name={rightIcon} size="sm" />
      )}
    </button>
  );
};

export default Button;
