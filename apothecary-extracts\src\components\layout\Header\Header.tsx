import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Container } from '../Container';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { NavigationMenu } from './NavigationMenu';
import { MobileMenu } from './MobileMenu';
import { LocationSelector } from './LocationSelector';
import { useUIStore } from '@/store/uiStore';
import { useCartStore } from '@/store/cartStore';
import { useLocationStore } from '@/store/locationStore';

export interface HeaderProps {
  className?: string;
}

export const Header: React.FC<HeaderProps> = ({ className }) => {
  const { 
    isMobileMenuOpen, 
    setMobileMenuOpen, 
    setLocationSelectorOpen,
    setCartOpen 
  } = useUIStore();
  
  const { getItemCount } = useCartStore();
  const { selectedLocation } = useLocationStore();
  
  const cartItemCount = getItemCount();

  return (
    <>
      <header className={cn(
        'sticky top-0 z-40 w-full bg-neutral-white border-b border-neutral-stone shadow-sm',
        className
      )}>
        <Container>
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 lg:w-10 lg:h-10 bg-primary-sage rounded-full flex items-center justify-center">
                  <Icon name="leaf" size="sm" className="text-neutral-white" />
                </div>
                <div className="hidden sm:block">
                  <span className="text-xl lg:text-2xl font-bold text-primary-sage font-accent">
                    Apothecary
                  </span>
                  <span className="text-xl lg:text-2xl font-light text-neutral-charcoal font-accent">
                    Extracts
                  </span>
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              <NavigationMenu />
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-2 lg:space-x-4">
              {/* Location Selector */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLocationSelectorOpen(true)}
                leftIcon="map-pin"
                className="hidden sm:flex"
              >
                {selectedLocation ? selectedLocation.name.split(' - ')[1] : 'Select Location'}
              </Button>

              {/* Search */}
              <Button
                variant="ghost"
                size="sm"
                className="p-2"
                aria-label="Search"
              >
                <Icon name="search" size="md" />
              </Button>

              {/* Cart */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCartOpen(true)}
                className="relative p-2"
                aria-label={`Shopping cart with ${cartItemCount} items`}
              >
                <Icon name="cart" size="md" />
                {cartItemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-secondary-amber text-neutral-charcoal text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                    {cartItemCount > 99 ? '99+' : cartItemCount}
                  </span>
                )}
              </Button>

              {/* Mobile Menu Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2"
                aria-label="Toggle mobile menu"
              >
                <Icon name={isMobileMenuOpen ? 'close' : 'menu'} size="md" />
              </Button>
            </div>
          </div>

          {/* Mobile Location Selector */}
          <div className="sm:hidden pb-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLocationSelectorOpen(true)}
              leftIcon="map-pin"
              fullWidth
              className="justify-start text-sm"
            >
              {selectedLocation ? selectedLocation.name : 'Select Location'}
            </Button>
          </div>
        </Container>
      </header>

      {/* Mobile Menu */}
      <MobileMenu 
        isOpen={isMobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
      />

      {/* Location Selector Modal */}
      <LocationSelector />
    </>
  );
};

export default Header;
